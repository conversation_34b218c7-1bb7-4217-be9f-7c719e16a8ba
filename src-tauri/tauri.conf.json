{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "CreatorOS", "version": "1.0.0", "identifier": "com.creatoros.app", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:3458", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "CreatorOS - AI Operating System for Creators", "width": 1280, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "center": true, "decorations": true}], "security": {"csp": null}}, "plugins": {"shell": {"open": true}, "dialog": null, "fs": null, "path": null}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["bin/*"], "publisher": "CreatorOS Inc.", "copyright": "© 2024 CreatorOS Inc. All rights reserved.", "shortDescription": "AI Operating System for Creators", "longDescription": "CreatorOS is the world's first AI-powered operating system designed specifically for content creators. It combines intelligent content management, predictive analytics, automated optimization, and seamless multi-platform publishing in one powerful desktop application.", "macOS": {"frameworks": [], "minimumSystemVersion": "10.15"}, "windows": {"digestAlgorithm": "sha256", "timestampUrl": "http://timestamp.digicert.com", "webviewInstallMode": {"type": "embed<PERSON><PERSON><PERSON><PERSON>", "silent": true}, "allowDowngrades": false}, "linux": {"appimage": {"bundleMediaFramework": true}, "deb": {"depends": []}}}}