use std::collections::HashMap;
use std::sync::Arc;
use std::path::Path;
use std::process::Command;
use std::time::Instant;
use tauri::{command, Window, State, Emitter, Manager};
use tokio::fs::File;
use tokio::io::AsyncWriteExt;
use tokio::sync::Mutex;
use serde::{Deserialize, Serialize};

// Configuration structures
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppConfig {
    pub downloads: DownloadConfig,
    pub network: NetworkConfig,
    pub security: SecurityConfig,
    pub logging: LoggingConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadConfig {
    pub max_concurrent: u32,
    pub retry_attempts: u32,
    pub timeout_seconds: u64,
    pub chunk_size_bytes: u64,
    pub progress_update_interval: u64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct NetworkConfig {
    pub user_agent: String,
    pub connection_timeout: u64,
    pub read_timeout: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub max_file_size: u64,
    pub sanitize_filenames: bool,
    pub validate_urls: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub enable_console: bool,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            downloads: DownloadConfig {
                max_concurrent: 3,
                retry_attempts: 3,
                timeout_seconds: 30,
                chunk_size_bytes: 1024 * 1024, // 1MB
                progress_update_interval: 500,
            },
            network: NetworkConfig {
                user_agent: "FlowDownload/1.0 (Desktop)".to_string(),
                connection_timeout: 10000,
                read_timeout: 30000,
            },
            security: SecurityConfig {
                max_file_size: 10 * 1024 * 1024 * 1024, // 10GB
                sanitize_filenames: true,
                validate_urls: true,
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                enable_console: true,
            },
        }
    }
}

mod commands;
mod auth;
mod upload;
mod database;
mod download;
mod performance;
mod security;
mod error_handling;
mod telemetry;
mod auto_update;
mod licensing;
mod ai;
mod sentry;

// Global state for production systems
lazy_static::lazy_static! {
  static ref TELEMETRY: Arc<Mutex<telemetry::TelemetrySystem>> = Arc::new(Mutex::new(
    telemetry::TelemetrySystem::new(telemetry::TelemetryConfig::default())
  ));
  
  static ref LICENSING: Arc<Mutex<licensing::LicensingSystem>> = Arc::new(Mutex::new(
    licensing::LicensingSystem::new(licensing::LicenseConfig {
      validation_endpoint: std::env::var("LICENSE_API_ENDPOINT").unwrap_or_else(|_| "https://api.flowdownload.com/license".to_string()),
      public_key: std::env::var("LICENSE_PUBLIC_KEY").unwrap_or_else(|_| "".to_string()),
      offline_grace_period_days: 7,
      validation_interval_hours: 24,
      enable_hardware_lock: true,
      allow_vm: false,
    })
  ));
  
  static ref AUTO_UPDATE: Arc<Mutex<Option<auto_update::AutoUpdateSystem>>> = Arc::new(Mutex::new(None));
}

fn initialize_production_systems() {
  // Initialize Sentry for error tracking
  crate::sentry::init_sentry();
  
  // Initialize telemetry
  log::info!("Initializing production systems for FlowDownload v{}", env!("CARGO_PKG_VERSION"));
  
  // Initialize auto-update system
  let update_config = auto_update::UpdateConfig {
    enabled: !cfg!(debug_assertions), // Disable in debug mode
    check_interval_hours: 12,
    endpoint: std::env::var("UPDATE_API_ENDPOINT").unwrap_or_else(|_| "https://api.flowdownload.com/updates".to_string()),
    public_key: std::env::var("UPDATE_PUBLIC_KEY").unwrap_or_else(|_| "".to_string()),
    force_update_below: None,
    silent_download: true,
    auto_install: false,
  };
  
  let update_channel = if cfg!(debug_assertions) {
    auto_update::UpdateChannel::Beta
  } else {
    auto_update::UpdateChannel::Stable
  };
  
  match auto_update::AutoUpdateSystem::new(update_config, env!("CARGO_PKG_VERSION"), update_channel) {
    Ok(system) => {
      let mut auto_update = AUTO_UPDATE.blocking_lock();
      *auto_update = Some(system);
    }
    Err(e) => {
      log::error!("Failed to initialize auto-update system: {}", e);
    }
  }
}

fn setup_panic_handler() {
  std::panic::set_hook(Box::new(|panic_info| {
    let backtrace = std::backtrace::Backtrace::capture();
    
    let message = if let Some(s) = panic_info.payload().downcast_ref::<&str>() {
      s.to_string()
    } else if let Some(s) = panic_info.payload().downcast_ref::<String>() {
      s.clone()
    } else {
      "Unknown panic".to_string()
    };
    
    let location = panic_info.location()
      .map(|loc| format!("{}:{}:{}", loc.file(), loc.line(), loc.column()))
      .unwrap_or_else(|| "Unknown location".to_string());
    
    log::error!("PANIC at {}: {}\nBacktrace:\n{}", location, message, backtrace);
    
    // Send crash report in production
    if !cfg!(debug_assertions) {
      // This would send to crash reporting service
      let crash_report = serde_json::json!({
        "message": message,
        "location": location,
        "backtrace": backtrace.to_string(),
        "version": env!("CARGO_PKG_VERSION"),
        "timestamp": chrono::Utc::now().to_rfc3339(),
      });
      
      // In production, send this to crash reporting service
      log::error!("Crash report: {}", crash_report);
    }
  }));
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  // Initialize production systems
  initialize_production_systems();
  
  let downloads: Downloads = Arc::new(Mutex::new(HashMap::new()));
  let upload_queue: commands::upload::UploadQueue = Arc::new(Mutex::new(HashMap::new()));
  
  tauri::Builder::default()
    .setup(|app| {
      // Set up panic handler for crash reporting
      setup_panic_handler();
      // Initialize database
      let app_data_dir = app.handle().path().app_data_dir()
        .expect("Failed to get app data directory");
      
      let db_pool = tauri::async_runtime::block_on(async {
        database::init_database(app_data_dir).await
      }).expect("Failed to initialize database");
      
      // Create download manager
      let download_manager = download::download_manager::DownloadManager::new(
        app.handle().clone(),
        db_pool.clone()
      );
      
      app.manage(db_pool);
      app.manage(download_manager);
      
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
        
        // Log system information for debugging
        #[cfg(debug_assertions)]
        {
          println!("Application name: {}", env!("CARGO_PKG_NAME"));
          
          if let Ok(exe_dir) = std::env::current_exe() {
            println!("Executable path: {:?}", exe_dir);
          }
          
          if let Ok(cwd) = std::env::current_dir() {
            println!("Current working directory: {:?}", cwd);
          }
        }
      }
      
      // Make sure bundled yt-dlp is executable
      #[cfg(not(target_os = "windows"))]
      {
        use std::path::PathBuf;
        
        // In Tauri v2, we need to resolve resource paths differently
        let resource_dir = app.handle().path().resource_dir().unwrap_or_else(|_| PathBuf::from("./"));
        let yt_dlp_path = resource_dir.join("bin").join("yt-dlp");
        
        if yt_dlp_path.exists() {
          println!("Making bundled yt-dlp executable: {:?}", yt_dlp_path);
          
          // Use std::process::Command to chmod +x the file
          match Command::new("chmod")
            .arg("+x")
            .arg(yt_dlp_path.to_string_lossy().to_string())
            .output() 
          {
            Ok(output) => {
              if output.status.success() {
                println!("Successfully made yt-dlp executable");
              } else {
                if let Ok(stderr) = String::from_utf8(output.stderr) {
                  println!("Failed to make yt-dlp executable: {}", stderr);
                } else {
                  println!("Failed to make yt-dlp executable");
                }
              }
            },
            Err(e) => {
              println!("Error running chmod: {}", e);
            }
          }
        } else {
          println!("Bundled yt-dlp not found in resources: {:?}", yt_dlp_path);
        }
      }
      
      Ok(())
    })
    .manage(downloads)
    .manage(upload_queue)
    .plugin(tauri_plugin_dialog::init())
    .plugin(tauri_plugin_fs::init())
    .plugin(tauri_plugin_shell::init())
    .invoke_handler(tauri::generate_handler![
      download_file,
      cancel_download,
      create_directory,
      check_directory_access,
      get_download_dir,
      get_home_dir,
      get_app_local_data_dir,
      check_file_permissions,
      commands::folder::get_downloads_folder,
      commands::folder::ensure_directory_exists,
      commands::folder::test_directory_access,
      commands::folder::path_exists,
      commands::folder::open_folder_in_explorer,
      commands::folder::open_folder_and_highlight_file,
      commands::folder::resolve_path,
      check_ytdlp_availability,
      get_ytdlp_executable_path,
      test_ytdlp_command,
      get_video_info,
      commands::media::get_media_info,
      commands::media::generate_thumbnail,
      commands::media::list_downloaded_files,
      commands::upload::queue_upload,
      commands::upload::start_upload,
      commands::upload::cancel_upload,
      commands::upload::get_upload_queue,
      commands::upload::authenticate_platform,
      commands::upload::handle_auth_callback,
      commands::optimize::get_optimization_presets,
      commands::optimize::optimize_for_platform,
      commands::optimize::batch_optimize,
      commands::optimize::estimate_optimization_time,
      commands::upload_history::get_upload_history,
      commands::upload_history::get_resumable_uploads,
      commands::upload_history::delete_upload_record,
      commands::upload_history::retry_upload,
      commands::upload_history::pause_upload,
      commands::upload_history::resume_upload,
      performance::get_performance_stats,
      security::get_security_audit_log,
      telemetry::track_event,
      auto_update::check_for_app_updates,
      auto_update::download_app_update,
      auto_update::install_app_update,
      licensing::activate_license_key,
      licensing::get_license_status,
      licensing::start_free_trial,
      commands::download::start_download,
      commands::download::pause_download,
      commands::download::resume_download,
      commands::download::cancel_download_new,
      commands::download::get_downloads,
      commands::download::get_active_downloads,
      commands::download::get_download_by_id,
      commands::download::delete_download_record,
      commands::download::get_system_info,
      commands::download::get_download_path,
      // AI Commands
      commands::ai::analyze_content,
      commands::ai::transcribe_content,
      commands::ai::detect_scenes,
      commands::ai::analyze_sentiment,
      commands::ai::predict_virality,
      commands::ai::remove_silence,
      commands::ai::enhance_audio,
      commands::ai::add_captions,
      commands::ai::export_edited_video,
      commands::ai::generate_highlights,
      commands::ai::load_creator_profile,
      commands::ai::get_trending_topics,
      commands::ai::predict_engagement,
      commands::ai::get_optimal_posting_times,
      commands::ai::generate_content_ideas,
      commands::ai::extract_topics,
      commands::ai::detect_objects,
      commands::ai::ai_optimize_for_platform,
      // Local AI commands
      commands::ai_local::get_media_metadata,
      commands::ai_local::analyze_content_local,
      commands::ai_local::detect_scenes_local,
      commands::ai_local::extract_thumbnail_local,
      commands::ai_local::detect_silence_local,
      commands::ai_local::analyze_content_groq,
      commands::ai_local::analyze_content_deepseek,
      commands::ai_local::transcribe_qwen,
      commands::ai_local::analyze_content_qwen,
      commands::ai_local::detect_scenes_qwen
    ])
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}

// Store active downloads using tokio::sync::Mutex for async compatibility
type Downloads = Arc<Mutex<HashMap<String, bool>>>; // download_id -> is_cancelled

#[derive(Clone, serde::Serialize)]
struct DownloadProgress {
  id: String,
  downloaded: u64,
  total: u64,
  speed: String,
  estimated_remaining: Option<String>, // Estimated time remaining in human readable format
  bytes_per_second: u64,              // Raw bytes per second for more accurate calculations
}

// Helper function to format human-readable time
fn format_duration(seconds: u64) -> String {
  if seconds < 60 {
    format!("{}s", seconds)
  } else if seconds < 3600 {
    format!("{}m {}s", seconds / 60, seconds % 60)
  } else {
    format!("{}h {}m", seconds / 3600, (seconds % 3600) / 60)
  }
}

// Helper commands to get system directories
#[command]
fn get_app_local_data_dir() -> Result<String, String> {
  // Using dirs crate instead of tauri API
  let app_data_dir = dirs::data_local_dir()
    .ok_or_else(|| "Could not get app data directory".to_string())?;
  
  println!("App data directory: {:?}", app_data_dir);
  Ok(app_data_dir.to_string_lossy().to_string())
}

#[command]
fn get_download_dir() -> Result<String, String> {
  // Using dirs crate instead of tauri API
  match dirs::download_dir() {
    Some(download_dir) => {
      println!("Download directory: {:?}", download_dir);
      let path_str = download_dir.to_string_lossy().to_string();

      // Check if the directory actually exists and is accessible
      if download_dir.exists() {
        println!("Download directory exists and is accessible");
        Ok(path_str)
      } else {
        println!("Download directory does not exist: {:?}", download_dir);
        Err(format!("Download directory does not exist: {}", path_str))
      }
    },
    None => {
      println!("Could not determine download directory from system");
      Err("Could not get download directory from system".to_string())
    }
  }
}

#[command]
fn get_home_dir() -> Result<String, String> {
  // Using dirs crate instead of tauri API
  let home_dir = dirs::home_dir()
    .ok_or_else(|| "Could not get home directory".to_string())?;
  
  println!("Home directory: {:?}", home_dir);
  Ok(home_dir.to_string_lossy().to_string())
}

// Helper function to check file permissions
#[command]
async fn check_file_permissions(path: String) -> Result<bool, String> {
  println!("Checking file permissions for: {}", path);
  
  // First check if the file exists
  match tokio::fs::metadata(&path).await {
    Ok(metadata) => {
      // Check if it's a file
      if metadata.is_file() {
        // Try to open it for reading
        match tokio::fs::OpenOptions::new().read(true).open(&path).await {
          Ok(_) => {
            println!("Can read file: {}", path);
            
            // Try to open it for writing
            match tokio::fs::OpenOptions::new().write(true).open(&path).await {
              Ok(_) => {
                println!("Can write to file: {}", path);
                Ok(true)
              },
              Err(e) => {
                println!("Cannot write to file {}: {}", path, e);
                Err(format!("Cannot write to file: {}", e))
              }
            }
          },
          Err(e) => {
            println!("Cannot read file {}: {}", path, e);
            Err(format!("Cannot read file: {}", e))
          }
        }
      } else {
        println!("Path exists but is not a file: {}", path);
        Err("Path exists but is not a file".to_string())
      }
    },
    Err(_) => {
      // If file doesn't exist, check if we can create it
      if let Some(parent) = Path::new(&path).parent() {
        let parent_str = parent.to_string_lossy().to_string();
        
        match tokio::fs::metadata(&parent_str).await {
          Ok(parent_metadata) => {
            if parent_metadata.is_dir() {
              // Try to create a test file in the parent directory
              let test_file_path = Path::new(&parent_str).join(TEST_FILE_NAME);
              match tokio::fs::File::create(&test_file_path).await {
                Ok(_) => {
                  // Clean up the test file
                  let _ = tokio::fs::remove_file(&test_file_path).await;
                  println!("Can create file in directory: {}", parent_str);
                  Ok(true)
                },
                Err(e) => {
                  println!("Cannot create file in directory {}: {}", parent_str, e);
                  Err(format!("Cannot create file in directory: {}", e))
                }
              }
            } else {
              println!("Parent path exists but is not a directory: {}", parent_str);
              Err("Parent path exists but is not a directory".to_string())
            }
          },
          Err(_) => {
            println!("Parent directory does not exist: {}", parent_str);
            Err("Parent directory does not exist".to_string())
          }
        }
      } else {
        println!("Cannot determine parent directory for: {}", path);
        Err("Cannot determine parent directory".to_string())
      }
    }
  }
}

// Helper function to check if we can create a directory
#[command]
async fn create_directory(path: String) -> Result<bool, String> {
  println!("Attempting to create directory: {}", path);
  
  // Check if the path exists
  let path_exists = tokio::fs::metadata(&path).await.is_ok();
  
  if path_exists {
    println!("Directory already exists: {}", path);
    // Try to create a test file to verify write permissions
    let test_file_path = Path::new(&path).join(TEST_FILE_NAME);
    match tokio::fs::File::create(&test_file_path).await {
      Ok(_) => {
        // Clean up the test file
        let _ = tokio::fs::remove_file(&test_file_path).await;
        println!("Have write permissions to directory: {}", path);
        Ok(true)
      }
      Err(e) => {
        println!("Cannot write to directory {}: {}", path, e);
        Err(format!("Cannot write to directory: {}", e))
      }
    }
  } else {
    // Try to create the directory
    match tokio::fs::create_dir_all(&path).await {
      Ok(_) => {
        println!("Successfully created directory: {}", path);
        
        // Verify we can write to it
        let test_file_path = Path::new(&path).join(TEST_FILE_NAME);
        match tokio::fs::File::create(&test_file_path).await {
          Ok(_) => {
            // Clean up the test file
            let _ = tokio::fs::remove_file(&test_file_path).await;
            println!("Have write permissions to created directory: {}", path);
            Ok(true)
          }
          Err(e) => {
            println!("Cannot write to created directory {}: {}", path, e);
            Err(format!("Cannot write to created directory: {}", e))
          }
        }
      }
      Err(e) => {
        println!("Failed to create directory {}: {}", path, e);
        Err(format!("Failed to create directory: {}", e))
      }
    }
  }
}

// Helper function to check if we have access to a directory
#[command]
async fn check_directory_access(path: String) -> Result<bool, String> {
  println!("Checking access to directory: {}", path);
  
  // First check if the directory exists
  if let Ok(metadata) = tokio::fs::metadata(&path).await {
    if metadata.is_dir() {
      println!("Directory exists: {}", path);
      
      // Try to create a test file
      let test_file_path = Path::new(&path).join(TEST_FILE_NAME);
      match tokio::fs::File::create(&test_file_path).await {
        Ok(_) => {
          // Clean up the test file
          let _ = tokio::fs::remove_file(&test_file_path).await;
          println!("Have write permissions to directory: {}", path);
          Ok(true)
        }
        Err(e) => {
          println!("Cannot write to directory {}: {}", path, e);
          Err(format!("Cannot write to directory: {}", e))
        }
      }
    } else {
      println!("Path exists but is not a directory: {}", path);
      Err("Path exists but is not a directory".to_string())
    }
  } else {
    println!("Directory does not exist: {}", path);
    
    // Try to create it
    match tokio::fs::create_dir_all(&path).await {
      Ok(_) => {
        println!("Created directory: {}", path);
        
        // Verify we can write to it
        let test_file_path = Path::new(&path).join(TEST_FILE_NAME);
        match tokio::fs::File::create(&test_file_path).await {
          Ok(_) => {
            // Clean up the test file
            let _ = tokio::fs::remove_file(&test_file_path).await;
            println!("Have write permissions to created directory: {}", path);
            Ok(true)
          }
          Err(e) => {
            println!("Cannot write to created directory {}: {}", path, e);
            // Try to clean up
            let _ = tokio::fs::remove_dir(&path).await;
            Err(format!("Cannot write to created directory: {}", e))
          }
        }
      }
      Err(e) => {
        println!("Could not create directory {}: {}", path, e);
        Err(format!("Could not create directory: {}", e))
      }
    }
  }
}

// Configuration for external tools
const YTDLP_BUNDLED_PATH: &str = "bin/yt-dlp";
const YTDLP_UNIX_PATHS: &[&str] = &[
  "/usr/local/bin/yt-dlp",
  "/opt/homebrew/bin/yt-dlp",
  "/usr/bin/yt-dlp",
  "/opt/local/bin/yt-dlp",
];
const YTDLP_WINDOWS_PATHS: &[&str] = &[
  "C:\\Program Files\\yt-dlp\\yt-dlp.exe",
  "C:\\yt-dlp\\yt-dlp.exe",
  "C:\\Tools\\yt-dlp\\yt-dlp.exe",
];

// ENTERPRISE-GRADE yt-dlp configuration for ANY file size at maximum speed
fn get_ytdlp_args(fragment_count: u8) -> Vec<&'static str> {
  vec![
    "--newline",              // Separate output lines for easier parsing
    "--no-warnings",          // Suppress warnings
    "--progress",             // Show progress bar
    "--no-colors",            // Disable colors in output for easier parsing
    "--concurrent-fragments", Box::leak(fragment_count.to_string().into_boxed_str()), // Dynamic fragment count (up to 32)
    "--buffer-size", "64M",   // MASSIVE buffer for enterprise downloads
    "--http-chunk-size", "52428800", // 50MB chunks for maximum throughput
    "--retries", "10",        // More retries for massive files
    "--fragment-retries", "10", // More fragment retries for reliability
    "--retry-sleep", "5", // Sleep 5 seconds between retries
    "--socket-timeout", "120", // Extended timeout for large files  
    "--hls-prefer-native",    // Native HLS for better performance
    "--external-downloader-args", "ffmpeg:-threads 0", // Use all CPU cores
    "--no-check-certificate", // Skip cert check for maximum speed (careful!)
    "--geo-bypass",           // Bypass geo restrictions
    "--ignore-errors",        // Continue on non-fatal errors
    "--cookies-from-browser", "safari", // Use Safari cookies to bypass bot detection
    "-f",                     // Format selector flag
  ]
}
const YTDLP_FORMAT: &str = "best[ext=mp4]/best";  // Prefer mp4, fallback to best

// Test file configuration
const TEST_FILE_NAME: &str = "flowdownload_test.tmp";

// Video file extensions for search detection
#[allow(dead_code)]
const VIDEO_EXTENSIONS: &[&str] = &[".mp4", ".mkv", ".webm", ".avi", ".mov"];

// No domain restrictions - support all websites

// Helper function to build yt-dlp format string based on quality and format
fn build_format_string(quality: Option<&str>, format: Option<&str>) -> String {
  let quality_str = quality.unwrap_or("best");
  let format_str = format.unwrap_or("mp4");
  
  // Handle special audio-only qualities
  if quality_str.starts_with("audio-") {
    let audio_quality = match quality_str {
      "audio-high" => "bestaudio[abr>=256]",
      "audio-medium" => "bestaudio[abr>=128]",
      "audio-low" => "bestaudio[abr>=64]",
      _ => "bestaudio"
    };
    
    // For audio, we'll post-process to the desired format
    return format!("{}/bestaudio", audio_quality);
  }
  
  // Build video format string - prioritize highest quality with reliable merge
  let format_string = match quality_str {
    "best" => {
      // Get absolute best quality available - no compromises on quality
      "bestvideo+bestaudio/best".to_string()
    },
    "4k" | "2160p" => {
      // Best 4K quality available
      "bestvideo[height<=2160]+bestaudio/best".to_string()
    },
    "1440p" => {
      // Best 1440p (2K) quality
      "bestvideo[height<=1440]+bestaudio/best".to_string()
    },
    "1080p" => {
      // Best 1080p Full HD quality
      "bestvideo[height<=1080]+bestaudio/best".to_string()
    },
    "720p" => {
      // Best 720p HD quality
      "bestvideo[height<=720]+bestaudio/best".to_string()
    },
    "480p" => {
      // 480p SD
      "bestvideo[height<=480]+bestaudio".to_string()
    },
    "360p" => {
      // 360p basic
      "bestvideo[height<=360]+bestaudio".to_string()
    },
    _ => {
      // Default to highest quality available
      "bestvideo+bestaudio/best".to_string()
    }
  };
  
  println!("📐 Built format string: {}", format_string);
  format_string
}

// Helper function to check if yt-dlp is installed or available
fn is_ytdlp_available(app_handle: &tauri::AppHandle) -> bool {
  println!("🔍 Checking yt-dlp availability...");
  
  // First check bundled version in our resources directory
  let resource_dir = match app_handle.path().resource_dir() {
    Ok(path) => {
      println!("📁 Resource directory: {:?}", path);
      path
    },
    Err(e) => {
      println!("❌ Error getting resource dir: {}", e);
      return false;
    }
  };
  
  let bundled_path = resource_dir.join(YTDLP_BUNDLED_PATH);
  println!("🔍 Checking bundled yt-dlp at: {:?}", bundled_path);
  if bundled_path.exists() {
    println!("✅ Found bundled yt-dlp at: {:?}", bundled_path);
    return true;
  } else {
    println!("❌ No bundled yt-dlp found");
  }
  
  // Check common locations based on platform
  let paths = if cfg!(target_os = "windows") {
    YTDLP_WINDOWS_PATHS
  } else {
    YTDLP_UNIX_PATHS
  };
  
  println!("🔍 Checking common yt-dlp locations...");
  for path in paths {
    println!("  Checking: {}", path);
    if Path::new(path).exists() {
      println!("✅ Found yt-dlp at: {}", path);
      return true;
    }
  }
  println!("❌ yt-dlp not found in common locations");
  
  // Try using which command on Unix-like systems
  #[cfg(not(target_os = "windows"))]
  {
    println!("🔍 Checking PATH using 'which' command...");
    match Command::new("which").arg("yt-dlp").output() {
      Ok(output) => {
        if output.status.success() {
          let path_output = String::from_utf8_lossy(&output.stdout).trim().to_string();
          println!("✅ Found yt-dlp in PATH: {}", path_output);
          return true;
        } else {
          println!("❌ 'which yt-dlp' returned non-success status");
        }
      },
      Err(e) => {
        println!("❌ Error running 'which' command: {}", e);
      }
    }
  }
  
  // Try using where command on Windows
  #[cfg(target_os = "windows")]
  {
    match Command::new("where").arg("yt-dlp").output() {
      Ok(output) => {
        if output.status.success() {
          println!("Found yt-dlp in PATH");
          return true;
        }
      },
      Err(_) => {}
    }
  }
  
  // If we get here, yt-dlp is not available
  println!("❌ yt-dlp not found anywhere - all detection methods failed");
  false
}

// Get the path to yt-dlp executable
fn get_ytdlp_path(app_handle: &tauri::AppHandle) -> Option<String> {
  // First try system installation (preferred)
  #[cfg(not(target_os = "windows"))]
  {
    match Command::new("which").arg("yt-dlp").output() {
      Ok(output) => {
        if output.status.success() {
          if let Ok(path) = String::from_utf8(output.stdout) {
            let path = path.trim();
            if !path.is_empty() {
              println!("Using system yt-dlp at: {}", path);
              return Some(path.to_string());
            }
          }
        }
      },
      Err(_) => {}
    }
  }
  
  // Try bundled version as fallback
  let resource_dir = match app_handle.path().resource_dir() {
    Ok(path) => path,
    Err(_) => return None,
  };
  
  let bundled_path = resource_dir.join(YTDLP_BUNDLED_PATH);
  if bundled_path.exists() {
    println!("Using bundled yt-dlp at: {:?}", bundled_path);
    return Some(bundled_path.to_string_lossy().to_string());
  }
  
  // Check common locations based on platform
  let paths = if cfg!(target_os = "windows") {
    YTDLP_WINDOWS_PATHS
  } else {
    YTDLP_UNIX_PATHS
  };
  
  for path in paths {
    if Path::new(path).exists() {
      return Some(path.to_string());
    }
  }
  
  // Try using which command on Unix-like systems
  #[cfg(not(target_os = "windows"))]
  {
    match Command::new("which").arg("yt-dlp").output() {
      Ok(output) => {
        if output.status.success() {
          if let Ok(path) = String::from_utf8(output.stdout) {
            let path = path.trim();
            if !path.is_empty() {
              return Some(path.to_string());
            }
          }
        }
      },
      Err(_) => {}
    }
  }
  
  // Try using where command on Windows
  #[cfg(target_os = "windows")]
  {
    match Command::new("where").arg("yt-dlp").output() {
      Ok(output) => {
        if output.status.success() {
          if let Ok(path) = String::from_utf8(output.stdout) {
            let path = path.trim();
            if !path.is_empty() {
              return Some(path.to_string());
            }
          }
        }
      },
      Err(_) => {}
    }
  }
  
  None
}

#[command]
async fn download_file(
  window: Window,
  downloads: State<'_, Downloads>,
  id: String,
  url: String,
  file_path: String,
  quality: Option<String>,
  format: Option<String>,
) -> Result<(), String> {
  // Input validation
  println!("📥 download_file called with ID: {}", id);
  if id.is_empty() {
    return Err("Download ID cannot be empty".to_string());
  }
  if url.is_empty() {
    return Err("URL cannot be empty".to_string());
  }
  if file_path.is_empty() {
    return Err("File path cannot be empty".to_string());
  }

  // Security: Sanitize URL
  let mut security = security::SECURITY_MANAGER.lock().await;
  let sanitized_url = security.sanitize_url(&url)
    .map_err(|e| format!("URL validation failed: {}", e))?;
  
  // Check if it's a search term or valid URL
  let is_valid_url = sanitized_url.starts_with("http://") || sanitized_url.starts_with("https://");
  let final_url = if sanitized_url.starts_with("search:") {
    sanitized_url.trim_start_matches("search:").to_string()
  } else {
    sanitized_url.clone()
  };

  // Validate file path doesn't contain dangerous characters
  if file_path.contains("..") || file_path.contains('\0') {
    security.audit_download("download_start", &url, "failed", Some("Invalid file path".to_string())).await;
    return Err("Invalid file path".to_string());
  }
  
  // Performance: Get optimal fragment count
  let file_size_estimate = 100 * 1024 * 1024; // 100MB estimate, will be refined later
  let domain = if is_valid_url {
    url::Url::parse(&final_url)
      .ok()
      .and_then(|u| u.host_str().map(|h| h.to_string()))
      .unwrap_or_default()
  } else {
    "youtube.com".to_string() // Default for searches
  };
  
  let optimal_fragments = performance::PERFORMANCE_MONITOR
    .get_optimal_fragment_count(file_size_estimate, &domain)
    .await;
  
  println!("🚀 Using {} concurrent fragments for optimal performance", optimal_fragments);

  // Add to active downloads
  {
    let mut downloads_map = downloads.lock().await;
    downloads_map.insert(id.clone(), false);
  }

  // Log the file path for debugging
  println!("Attempting to download file to: {}", file_path);
  
  // Always try to use yt-dlp for maximum compatibility with all sites
  let requires_ytdlp = true;
  
  if requires_ytdlp {
    // Check if yt-dlp is available
    let app_handle = window.app_handle();
    if !is_ytdlp_available(&app_handle) {
      let error_msg = "yt-dlp is not installed. Please install yt-dlp to enable downloads.";
      println!("{}", error_msg);
      let _ = window.emit("download-error", &id);
      
      // Remove from active downloads
      {
        let mut downloads_map = downloads.lock().await;
        downloads_map.remove(&id);
      }
      
      return Err(error_msg.to_string());
    }
    
    // Get the path to yt-dlp
    let ytdlp_path = match get_ytdlp_path(&app_handle) {
      Some(path) => {
        println!("✅ Found yt-dlp at path: {}", path);
        path
      },
      None => {
        let error_msg = "Could not determine path to yt-dlp";
        println!("❌ {}", error_msg);
        let _ = window.emit("download-error", &id);
        
        // Remove from active downloads
        {
          let mut downloads_map = downloads.lock().await;
          downloads_map.remove(&id);
        }
        
        return Err(error_msg.to_string());
      }
    };
    
    // Use std::process::Command to download the video
    println!("Starting yt-dlp download from URL: {}", url);
    
    // Make sure parent directory exists
    let path = Path::new(&file_path);
    if let Some(parent) = path.parent() {
      let parent_str = parent.to_string_lossy().to_string();
      match create_directory(parent_str).await {
        Ok(_) => println!("Parent directory is ready for download"),
        Err(e) => return Err(format!("Error with parent directory: {}", e)),
      }
    }
    
    // Execute yt-dlp with appropriate options
    let mut command = if cfg!(target_os = "windows") {
      let mut cmd = Command::new("cmd");
      cmd.args(["/C", &ytdlp_path]);
      cmd
    } else {
      Command::new(&ytdlp_path)
    };
    
    // Configure command arguments with dynamic fragment count
    let ytdlp_args = get_ytdlp_args(optimal_fragments);
    for arg in ytdlp_args {
      command.arg(arg);
    }
    
    // Build format string based on quality and format parameters
    let format_string = build_format_string(quality.as_deref(), format.as_deref());
    command
      .arg(&format_string)          // Format selector with quality/format
      .arg("--no-simulate");        // Don't simulate, actually download
    
    // Add format-specific options
    if let Some(fmt) = &format {
      match fmt.as_str() {
        "mp3" => {
          // Convert to MP3 with good quality
          command.arg("--extract-audio");
          command.arg("--audio-format");
          command.arg("mp3");
          command.arg("--audio-quality");
          command.arg("0"); // Best quality
        },
        "m4a" => {
          command.arg("--extract-audio");
          command.arg("--audio-format");
          command.arg("m4a");
          command.arg("--audio-quality");
          command.arg("0");
        },
        "wav" => {
          command.arg("--extract-audio");
          command.arg("--audio-format");
          command.arg("wav");
        },
        "flac" => {
          command.arg("--extract-audio");
          command.arg("--audio-format");
          command.arg("flac");
        },
        "mkv" => {
          // MKV: Just merge the streams without any processing
          // Let yt-dlp handle the merge with its default settings
        },
        "mp4" => {
          // MP4: Only add faststart for better streaming, let yt-dlp handle codecs
          command.arg("--postprocessor-args");
          command.arg("ffmpeg:-movflags +faststart");
        },
        _ => {
          // Other formats: minimal intervention, let yt-dlp do its job
          if !["webm", "avi", "mov"].contains(&fmt.as_str()) {
            command.arg("--merge-output-format");
            command.arg("mp4"); // Ensure we get MP4 output
          }
        }
      }
    }
    
    // Add merge output format to ensure proper container
    command.arg("--merge-output-format");
    command.arg(format.as_deref().unwrap_or("mp4"));
    
    command
      .arg("-o")                     // Output file name
      .arg(&file_path);
    
    // Handle different URL types
    if is_valid_url {
      command.arg(&final_url);
    } else {
      // If it's not a valid URL but matches our criteria (like "watch.mp4"),
      // search YouTube for that term
      let search_query = format!("ytsearch:{}", final_url);
      println!("🔍 Converting filename to YouTube search: {}", search_query);
      command.arg(&search_query);
    }
    
    // Track download start time for performance metrics
    let download_start = Instant::now();
    let retry_count = 0u32;
    
    // Add stdout and stderr capture
    command.stdout(std::process::Stdio::piped());
    command.stderr(std::process::Stdio::piped());
    
    // Disable buffering to avoid broken pipe
    command.env("PYTHONUNBUFFERED", "1");
    
    println!("🚀 Executing yt-dlp command: {:?}", command);
    println!("📍 Output file path: {}", file_path);
    println!("🔗 URL: {}", final_url);
    println!("🔧 yt-dlp path: {}", ytdlp_path);
    println!("⚡ Using {} concurrent fragments", optimal_fragments);
    
    // Start the command with progress streaming
    use std::io::{BufRead, BufReader};
    use std::thread;
    
    println!("🚀 About to spawn yt-dlp process...");
    println!("🔧 Command to execute: {:?}", command);
    
    match command.spawn() {
      Ok(mut child) => {
        println!("✅ yt-dlp process spawned successfully with PID: {:?}", child.id());
        
        // Get stdout for progress monitoring
        let stdout = child.stdout.take().ok_or("Failed to capture stdout")?;
        let stderr = child.stderr.take().ok_or("Failed to capture stderr")?;
        
        println!("✅ Stdout and stderr captured successfully");
        
        // Clone window and id for the thread
        let window_clone = window.clone();
        let id_clone = id.clone();
        let _id_clone2 = id.clone();
        
        // Spawn thread to read stdout and parse progress
        let stdout_thread = thread::spawn(move || {
          let reader = BufReader::new(stdout);
          let mut last_progress = 0;
          
          for line in reader.lines() {
            if let Ok(line) = line {
              println!("[yt-dlp] {}", line);
              
              // Parse download progress (yt-dlp format: [download]  10.5% of 234.56MiB at 1.23MiB/s ETA 00:45)
              if line.contains("[download]") && line.contains("%") {
                // Extract percentage - find the first number followed by %
                if let Some(percent_pos) = line.find("%") {
                  // Work backwards from % to find the start of the number
                  let mut start_pos = percent_pos;
                  let line_bytes = line.as_bytes();
                  
                  // Skip back to find the start of the number
                  while start_pos > 0 {
                    let prev_byte = line_bytes[start_pos - 1];
                    if prev_byte.is_ascii_digit() || prev_byte == b'.' {
                      start_pos -= 1;
                    } else {
                      break;
                    }
                  }
                  
                  if start_pos < percent_pos {
                    if let Ok(progress) = line[start_pos..percent_pos].trim().parse::<f32>() {
                      let progress_int = progress as u64;
                      
                      // Extract speed safely
                      let speed = if let Some(at_pos) = line.find(" at ") {
                        let at_end = at_pos + 4;
                        if at_end < line.len() {
                          if let Some(speed_end) = line[at_end..].find(" ") {
                            line[at_end..at_end+speed_end].to_string()
                          } else if at_end < line.len() {
                            line[at_end..].trim().to_string()
                          } else {
                            "Calculating...".to_string()
                          }
                        } else {
                          "Calculating...".to_string()
                        }
                      } else {
                        "Calculating...".to_string()
                      };
                      
                      // Extract ETA safely
                      let eta = if let Some(eta_pos) = line.find("ETA ") {
                        let eta_start = eta_pos + 4;
                        if eta_start < line.len() {
                          Some(line[eta_start..].trim().to_string())
                        } else {
                          None
                        }
                      } else {
                        None
                      };
                      
                      // Only emit if progress changed
                      if progress_int != last_progress {
                        last_progress = progress_int;
                        let _ = window_clone.emit("download-progress", &DownloadProgress {
                          id: id_clone.clone(),
                          downloaded: progress_int,
                          total: 100,
                          speed: speed.clone(),
                          estimated_remaining: eta,
                          bytes_per_second: 0, // We could calculate this from speed
                        });
                      }
                    }
                  }
                }
              }
            }
          }
        });
        
        // Collect stderr
        let stderr_thread = thread::spawn(move || {
          let reader = BufReader::new(stderr);
          let mut stderr_output = String::new();
          for line in reader.lines() {
            if let Ok(line) = line {
              println!("[yt-dlp stderr] {}", line);
              stderr_output.push_str(&line);
              stderr_output.push('\n');
            }
          }
          stderr_output
        });
        
        // Wait for the process to complete
        match child.wait() {
          Ok(status) => {
            // Wait for threads to complete
            let _ = stdout_thread.join();
            let stderr_output = stderr_thread.join().unwrap_or_default();
            
            if status.success() {
              println!("✅ yt-dlp download completed successfully");
              
              // Performance: Record download metrics
              let download_time = download_start.elapsed();
              let file_size = tokio::fs::metadata(&file_path).await
                .map(|m| m.len())
                .unwrap_or(0);
              let average_speed = if download_time.as_secs() > 0 {
                file_size as f64 / download_time.as_secs_f64()
              } else {
                0.0
              };
              
              let metrics = performance::DownloadMetrics {
                domain: domain.clone(),
                file_size,
                download_time,
                average_speed,
                peak_speed: average_speed, // TODO: Track actual peak
                fragments_used: optimal_fragments,
                retry_count,
                success: true,
              };
              
              performance::PERFORMANCE_MONITOR.record_download(metrics).await;
              
              // Security: Audit successful download
              security.audit_download("download_complete", &url, "success", 
                Some(format!("Size: {} bytes, Time: {:.2}s", file_size, download_time.as_secs_f64()))).await;
              
              // Report progress as completed
              let _ = window.emit("download-progress", &DownloadProgress {
                id: id.clone(),
                downloaded: 100,
                total: 100,
                speed: "Completed".to_string(),
                estimated_remaining: None,
                bytes_per_second: 0,
              });
              
              // Emit completion
              let _ = window.emit("download-complete", &id);
              
              // Remove from active downloads
              {
                let mut downloads_map = downloads.lock().await;
                downloads_map.remove(&id);
              }
              
              return Ok(());
            } else {
              let error_msg = format!("yt-dlp failed with status: {:?}\nStderr: {}", 
                                     status, stderr_output);
              println!("❌ {}", error_msg);
              
              // Performance: Record failed download
              let download_time = download_start.elapsed();
              let metrics = performance::DownloadMetrics {
                domain: domain.clone(),
                file_size: 0,
                download_time,
                average_speed: 0.0,
                peak_speed: 0.0,
                fragments_used: optimal_fragments,
                retry_count,
                success: false,
              };
              
              performance::PERFORMANCE_MONITOR.record_download(metrics).await;
              
              // Security: Audit failed download
              security.audit_download("download_failed", &url, "error", Some(error_msg.clone())).await;
              
              // Emit error with details
              let _ = window.emit("download-error", serde_json::json!({
                "id": &id,
                "error": &error_msg
              }));
              
              // Remove from active downloads
              {
                let mut downloads_map = downloads.lock().await;
                downloads_map.remove(&id);
              }
              
              return Err(error_msg);
            }
          },
          Err(e) => {
            let error_msg = format!("Failed to wait for yt-dlp: {}", e);
            println!("❌ {}", error_msg);
            let _ = window.emit("download-error", serde_json::json!({
              "id": &id,
              "error": &error_msg
            }));
            
            // Remove from active downloads
            {
              let mut downloads_map = downloads.lock().await;
              downloads_map.remove(&id);
            }
            
            return Err(error_msg);
          }
        }
      },
      Err(e) => {
        let error_msg = format!("Failed to spawn yt-dlp: {}", e);
        println!("❌ Spawn error: {}", error_msg);
        println!("❌ yt-dlp path used: {}", ytdlp_path);
        println!("❌ Command that failed: {:?}", command);
        let _ = window.emit("download-error", &id);
        
        // Remove from active downloads
        {
          let mut downloads_map = downloads.lock().await;
          downloads_map.remove(&id);
        }
        
        return Err(error_msg);
      }
    }
  }
  
  // Make sure the file_path is valid
  let path = Path::new(&file_path);
  if let Some(parent) = path.parent() {
    let parent_str = parent.to_string_lossy().to_string();
    println!("Ensuring parent directory exists: {}", parent_str);
    
    match create_directory(parent_str).await {
      Ok(_) => println!("Parent directory is ready for download"),
      Err(e) => return Err(format!("Error with parent directory: {}", e)),
    }
  } else {
    println!("No parent directory found for path: {}", file_path);
  }

  // Check if we have permission to write to the file
  match check_file_permissions(file_path.clone()).await {
    Ok(_) => println!("We have permission to write to the file"),
    Err(e) => {
      println!("File permission error: {}", e);
      return Err(format!("File permission error: {}", e));
    }
  }

  let client = reqwest::Client::new();
  
  // Get the file size first
  let response = client.head(&url).send().await
    .map_err(|e| format!("Failed to fetch URL info: {}", e))?;
  
  let total_size = response.content_length().unwrap_or(0);
  
  // Start the actual download
  let mut response = client.get(&url).send().await
    .map_err(|e| format!("Failed to start download: {}", e))?;
  
  if !response.status().is_success() {
    return Err(format!("Server returned status: {}", response.status()));
  }

  // Create the file
  let mut file = match File::create(&file_path).await {
    Ok(file) => file,
    Err(e) => {
      println!("Error creating file at {}: {}", file_path, e);
      return Err(format!("Failed to create file: {}", e));
    }
  };

  let mut downloaded = 0u64;
  let start_time = std::time::Instant::now();
  let mut last_update_time = start_time;
  let mut last_downloaded = 0u64;
  let update_interval = std::time::Duration::from_millis(500); // Update UI every 500ms
  
  // For calculating a smoother speed estimate
  let mut recent_speeds = Vec::with_capacity(5); // Store the last 5 speed measurements
  #[allow(unused_assignments)]
  let mut bytes_per_second: u64 = 0;

  // Download with progress reporting
  while let Some(chunk) = response.chunk().await
    .map_err(|e| format!("Failed to read chunk: {}", e))? {
    
    // Check if download was cancelled
    {
      let downloads_map = downloads.lock().await;
      if downloads_map.get(&id).copied().unwrap_or(false) {
        // Clean up and return
        let _ = tokio::fs::remove_file(&file_path).await;
        return Err("Download cancelled".to_string());
      }
    }

    match file.write_all(&chunk).await {
      Ok(_) => {
        downloaded += chunk.len() as u64;
      },
      Err(e) => {
        println!("Error writing to file {}: {}", file_path, e);
        return Err(format!("Failed to write to file: {}", e));
      }
    }
    
    // Only update the UI periodically to avoid overwhelming it
    let now = std::time::Instant::now();
    if now.duration_since(last_update_time) >= update_interval {
      // Calculate current speed
      let elapsed_since_last = now.duration_since(last_update_time).as_secs_f64();
      let bytes_since_last = downloaded - last_downloaded;
      
      if elapsed_since_last > 0.0 {
        let current_speed = bytes_since_last as f64 / elapsed_since_last;
        
        // Add to recent speeds for smoother calculation
        recent_speeds.push(current_speed);
        if recent_speeds.len() > 5 {
          recent_speeds.remove(0);
        }
        
        // Calculate average speed from recent measurements
        let avg_speed = if recent_speeds.is_empty() {
          0.0
        } else {
          recent_speeds.iter().sum::<f64>() / recent_speeds.len() as f64
        };
        bytes_per_second = avg_speed as u64;
        
        // Calculate estimated time remaining
        let mut estimated_remaining = None;
        if total_size > 0 && bytes_per_second > 0 {
          let remaining_bytes = total_size - downloaded;
          let seconds_remaining = remaining_bytes as f64 / avg_speed;
          if seconds_remaining.is_finite() && seconds_remaining > 0.0 {
            estimated_remaining = Some(format_duration(seconds_remaining as u64));
          }
        }
        
        // Format human-readable speed
        let speed = if avg_speed > 1024.0 * 1024.0 {
          format!("{:.1} MB/s", avg_speed / (1024.0 * 1024.0))
        } else if avg_speed > 1024.0 {
          format!("{:.1} KB/s", avg_speed / 1024.0)
        } else {
          format!("{:.0} B/s", avg_speed)
        };

        // Emit progress using the Emitter trait
        let progress = DownloadProgress {
          id: id.clone(),
          downloaded,
          total: total_size,
          speed,
          estimated_remaining,
          bytes_per_second,
        };
        
        let _ = window.emit("download-progress", &progress);
        
        // Update for next interval
        last_update_time = now;
        last_downloaded = downloaded;
      }
    }
  }

  match file.flush().await {
    Ok(_) => println!("Successfully completed download to: {}", file_path),
    Err(e) => println!("Error flushing file {}: {}", file_path, e)
  }

  // Calculate overall stats for the completed download
  let total_elapsed = start_time.elapsed().as_secs_f64();
  let avg_speed = if total_elapsed > 0.0 {
    downloaded as f64 / total_elapsed
  } else {
    0.0
  };
  
  let final_speed = if avg_speed > 1024.0 * 1024.0 {
    format!("{:.1} MB/s", avg_speed / (1024.0 * 1024.0))
  } else if avg_speed > 1024.0 {
    format!("{:.1} KB/s", avg_speed / 1024.0)
  } else {
    format!("{:.0} B/s", avg_speed)
  };

  // Remove from active downloads
  {
    let mut downloads_map = downloads.lock().await;
    downloads_map.remove(&id);
  }

  // Emit final progress with accurate speed calculation
  let final_progress = DownloadProgress {
    id: id.clone(),
    downloaded,
    total: total_size,
    speed: final_speed,
    estimated_remaining: None,
    bytes_per_second: avg_speed as u64,
  };
  
  let _ = window.emit("download-progress", &final_progress);

  // Emit completion using the Emitter trait
  let _ = window.emit("download-complete", &id);
  
  Ok(())
}

#[command]
async fn cancel_download(
  downloads: State<'_, Downloads>,
  id: String,
) -> Result<(), String> {
  let mut downloads_map = downloads.lock().await;
  downloads_map.insert(id, true);
  Ok(())
}

// Command to check yt-dlp availability for debugging
#[command]
fn check_ytdlp_availability(app_handle: tauri::AppHandle) -> Result<bool, String> {
  let available = is_ytdlp_available(&app_handle);
  println!("yt-dlp availability check: {}", available);
  Ok(available)
}

// Command to get yt-dlp path for debugging
#[command]
fn get_ytdlp_executable_path(app_handle: tauri::AppHandle) -> Result<String, String> {
  match get_ytdlp_path(&app_handle) {
    Some(path) => {
      println!("yt-dlp path found: {}", path);
      Ok(path)
    }
    None => {
      println!("yt-dlp path not found");
      Err("yt-dlp not found".to_string())
    }
  }
}

// Command to test yt-dlp execution
#[command]
fn test_ytdlp_command(app_handle: tauri::AppHandle) -> Result<String, String> {
  match get_ytdlp_path(&app_handle) {
    Some(ytdlp_path) => {
      println!("🧪 Testing yt-dlp command execution...");
      
      // Run yt-dlp --version to test basic functionality
      let mut command = if cfg!(target_os = "windows") {
        let mut cmd = Command::new("cmd");
        cmd.args(["/C", &ytdlp_path]);
        cmd
      } else {
        Command::new(&ytdlp_path)
      };
      
      command.arg("--version");
      
      match command.output() {
        Ok(output) => {
          let stdout = String::from_utf8_lossy(&output.stdout);
          let stderr = String::from_utf8_lossy(&output.stderr);
          
          if output.status.success() {
            println!("✅ yt-dlp test successful: {}", stdout.trim());
            Ok(format!("yt-dlp version: {}", stdout.trim()))
          } else {
            let error_msg = format!("yt-dlp test failed: {}", stderr);
            println!("❌ {}", error_msg);
            Err(error_msg)
          }
        }
        Err(e) => {
          let error_msg = format!("Failed to execute yt-dlp test: {}", e);
          println!("❌ {}", error_msg);
          Err(error_msg)
        }
      }
    }
    None => {
      Err("yt-dlp not found".to_string())
    }
  }
}

// Command to get video info from URL
#[command]
async fn get_video_info(app_handle: tauri::AppHandle, url: String) -> Result<serde_json::Value, String> {
  let ytdlp_path = get_ytdlp_path(&app_handle)
    .ok_or_else(|| "yt-dlp not found".to_string())?;
  
  println!("🔍 Getting video info for URL: {}", url);
  
  // Build command to get video info
  let mut command = if cfg!(target_os = "windows") {
    let mut cmd = Command::new("cmd");
    cmd.args(["/C", &ytdlp_path]);
    cmd
  } else {
    Command::new(&ytdlp_path)
  };
  
  // Add arguments to dump JSON info without downloading
  command
    .arg("--dump-json")
    .arg("--no-playlist")
    .arg(&url);
  
  // Execute command
  match command.output() {
    Ok(output) => {
      if output.status.success() {
        let stdout = String::from_utf8_lossy(&output.stdout);
        // Parse JSON output
        match serde_json::from_str::<serde_json::Value>(&stdout) {
          Ok(json) => {
            println!("✅ Successfully extracted video info");
            Ok(json)
          }
          Err(e) => {
            println!("❌ Failed to parse video info JSON: {}", e);
            Err(format!("Failed to parse video info: {}", e))
          }
        }
      } else {
        let stderr = String::from_utf8_lossy(&output.stderr);
        println!("❌ yt-dlp failed to get video info: {}", stderr);
        Err(format!("Failed to get video info: {}", stderr))
      }
    }
    Err(e) => {
      println!("❌ Failed to execute yt-dlp: {}", e);
      Err(format!("Failed to execute yt-dlp: {}", e))
    }
  }
}
