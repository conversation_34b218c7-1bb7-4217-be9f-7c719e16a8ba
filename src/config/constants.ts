// Application Configuration Constants
// This file centralizes all configurable values to make the app dynamic

export interface AppConfig {
  app: {
    name: string;
    version: string;
    identifier: string;
    title: string;
    description: string;
  };
  window: {
    defaultWidth: number;
    defaultHeight: number;
    minWidth: number;
    minHeight: number;
    resizable: boolean;
    center: boolean;
  };
  server: {
    devPort: number;
    strictPort: boolean;
  };
  downloads: {
    maxConcurrent: number;
    minConcurrentFragments: number;
    maxConcurrentFragments: number;
    retryAttempts: number;
    timeoutSeconds: number;
    chunkSizeBytes: number;
    adaptiveChunkSize: boolean;
    minChunkSizeBytes: number;
    maxChunkSizeBytes: number;
    defaultQuality: string;
    supportedFormats: string[];
    progressUpdateInterval: number;
  };
  network: {
    userAgent: string;
    maxBandwidthMbps: number;
    connectionTimeout: number;
    readTimeout: number;
  };
  ui: {
    theme: 'light' | 'dark' | 'auto';
    notifications: boolean;
    soundEffects: boolean;
    minimizeToTray: boolean;
    autoSave: boolean;
    refreshInterval: number;
  };
  paths: {
    defaultDownloadDir: string;
    configDir: string;
    logDir: string;
    tempDir: string;
    pluginDir: string;
  };
  security: {
    allowedDomains: string[];
    maxFileSize: number;
    sanitizeFilenames: boolean;
    validateUrls: boolean;
    enableCertificatePinning: boolean;
    verifyContentIntegrity: boolean;
    scanForMalware: boolean;
    quarantineSuspiciousFiles: boolean;
    auditLogging: boolean;
  };
  plugins: {
    enabled: boolean;
    autoUpdate: boolean;
    allowUnsigned: boolean;
    maxPlugins: number;
  };
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    maxFileSize: number;
    maxFiles: number;
    enableConsole: boolean;
  };
  fileCategories: {
    video: string[];
    audio: string[];
    image: string[];
    document: string[];
    archive: string[];
  };
}

// Default configuration
export const DEFAULT_CONFIG: AppConfig = {
  app: {
    name: 'FlowDownload',
    version: '1.0.0',
    identifier: 'com.flowdownload.app',
    title: 'FlowDownload Desktop Pro',
    description: 'Modern, clean, sophisticated desktop downloader app',
  },
  window: {
    defaultWidth: 1280,
    defaultHeight: 800,
    minWidth: 800,
    minHeight: 600,
    resizable: true,
    center: true,
  },
  server: {
    devPort: 3458,
    strictPort: true,
  },
  downloads: {
    maxConcurrent: 3,
    minConcurrentFragments: 2,
    maxConcurrentFragments: 8,
    retryAttempts: 3,
    timeoutSeconds: 30,
    chunkSizeBytes: 1024 * 1024, // 1MB
    adaptiveChunkSize: true,
    minChunkSizeBytes: 1024 * 1024, // 1MB
    maxChunkSizeBytes: 50 * 1024 * 1024, // 50MB
    defaultQuality: 'auto',
    supportedFormats: ['mp4', 'mp3', 'webm', 'mkv', 'avi', 'mov', 'flv'],
    progressUpdateInterval: 500, // ms
  },
  network: {
    userAgent: 'FlowDownload/1.0 (Desktop)',
    maxBandwidthMbps: 0, // 0 = unlimited
    connectionTimeout: 10000, // ms
    readTimeout: 30000, // ms
  },
  ui: {
    theme: 'auto',
    notifications: true,
    soundEffects: false,
    minimizeToTray: true,
    autoSave: true,
    refreshInterval: 1000, // ms
  },
  paths: {
    defaultDownloadDir: '', // Will be set dynamically
    configDir: '', // Will be set dynamically
    logDir: '', // Will be set dynamically
    tempDir: '', // Will be set dynamically
    pluginDir: '', // Will be set dynamically
  },
  security: {
    allowedDomains: [], // No domain restrictions - support all websites
    maxFileSize: 10 * 1024 * 1024 * 1024, // 10GB
    sanitizeFilenames: true,
    validateUrls: false, // Accept any input, not just valid URLs
    enableCertificatePinning: true,
    verifyContentIntegrity: true,
    scanForMalware: false, // Optional integration
    quarantineSuspiciousFiles: true,
    auditLogging: true,
  },
  plugins: {
    enabled: true,
    autoUpdate: false,
    allowUnsigned: false,
    maxPlugins: 50,
  },
  logging: {
    level: 'info',
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    enableConsole: true,
  },
  fileCategories: {
    video: ['.mp4', '.webm', '.mkv', '.avi', '.mov', '.flv', '.m4v'],
    audio: ['.mp3', '.aac', '.ogg', '.wav', '.flac', '.m4a'],
    document: ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
    image: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
    archive: ['.zip', '.rar', '.7z', '.tar', '.gz'],
  },
};

// Quality options for downloads
export const QUALITY_OPTIONS = [
  { value: 'auto', label: 'Auto (Best Available)' },
  { value: '2160p', label: '4K (2160p)' },
  { value: '1440p', label: '2K (1440p)' },
  { value: '1080p', label: 'Full HD (1080p)' },
  { value: '720p', label: 'HD (720p)' },
  { value: '480p', label: 'SD (480p)' },
  { value: '360p', label: 'Low (360p)' },
  { value: 'audio', label: 'Audio Only' },
] as const;

// File type categories
export const FILE_CATEGORIES = {
  video: ['mp4', 'webm', 'mkv', 'avi', 'mov', 'flv', 'm4v'],
  audio: ['mp3', 'aac', 'ogg', 'wav', 'flac', 'm4a'],
  document: ['pdf', 'doc', 'docx', 'txt', 'rtf'],
  image: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
  archive: ['zip', 'rar', '7z', 'tar', 'gz'],
} as const;

// Platform-specific paths for external tools
export const EXTERNAL_TOOLS = {
  ytdlp: {
    bundledPath: 'bin/yt-dlp',
    commonPaths: {
      unix: [
        '/usr/local/bin/yt-dlp',
        '/opt/homebrew/bin/yt-dlp',
        '/usr/bin/yt-dlp',
        '/opt/local/bin/yt-dlp',
      ],
      windows: [
        'C:\\Program Files\\yt-dlp\\yt-dlp.exe',
        'C:\\yt-dlp\\yt-dlp.exe',
        'C:\\Tools\\yt-dlp\\yt-dlp.exe',
      ],
    },
  },
  ffmpeg: {
    bundledPath: 'bin/ffmpeg',
    commonPaths: {
      unix: [
        '/usr/local/bin/ffmpeg',
        '/opt/homebrew/bin/ffmpeg',
        '/usr/bin/ffmpeg',
        '/opt/local/bin/ffmpeg',
      ],
      windows: [
        'C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe',
        'C:\\ffmpeg\\bin\\ffmpeg.exe',
        'C:\\Tools\\ffmpeg\\bin\\ffmpeg.exe',
      ],
    },
  },
} as const;

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  INVALID_URL: 'Invalid URL format. Please enter a valid URL.',
  DOWNLOAD_FAILED: 'Download failed. Please try again.',
  PERMISSION_DENIED: 'Permission denied. Please check folder permissions.',
  DISK_FULL: 'Insufficient disk space. Please free up space and try again.',
  FILE_NOT_FOUND: 'File not found. The download may have been moved or deleted.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  UNSUPPORTED_FORMAT: 'Unsupported file format.',
  PLUGIN_ERROR: 'Plugin error occurred.',
  CONFIG_ERROR: 'Configuration error. Please check your settings.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  DOWNLOAD_COMPLETE: 'Download completed successfully!',
  DOWNLOAD_STARTED: 'Download started.',
  PLUGIN_INSTALLED: 'Plugin installed successfully.',
  SETTINGS_SAVED: 'Settings saved successfully.',
  FOLDER_CREATED: 'Folder created successfully.',
} as const;
