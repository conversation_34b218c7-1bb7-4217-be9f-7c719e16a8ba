import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import { useDownloadStore } from '../store/downloadStore';
import { selectDownloadFolder, formatPathForDisplay, validateDownloadPath, resolvePath } from '../utils/folderUtils';
import { FiDownload, FiPlus, FiTrash2, FiList, FiCopy, FiFolder } from 'react-icons/fi';

const LOCAL_STORAGE_BATCH_PATH_KEY = 'flowdownload_batchDownloadPath';

const BatchDownload: React.FC = () => {
  const [urls, setUrls] = useState<string[]>(['']);
  const [bulkText, setBulkText] = useState('');
  const [quality, setQuality] = useState('best');
  const [isBulkMode, setIsBulkMode] = useState(false);
  const [currentDownloadPath, setCurrentDownloadPath] = useState<string>('');
  const [isLoadingPath, setIsLoadingPath] = useState(true);
  const [isSelectingFolder, setIsSelectingFolder] = useState(false);
  const { addDownload, defaultDownloadPath, setDefaultDownloadPath, initializeDefaultPath, isInitialized } = useDownloadStore();

  const qualityOptions = [
    { value: 'best', label: 'Best Quality' },
    { value: '2160p', label: '4K (2160p)' },
    { value: '1440p', label: '2K (1440p)' },
    { value: '1080p', label: 'Full HD (1080p)' },
    { value: '720p', label: 'HD (720p)' },
    { value: '480p', label: 'SD (480p)' },
  ];

  // Load and initialize download path
  const initializePath = useCallback(async () => {
    setIsLoadingPath(true);
    
    // First, ensure the store's default path is initialized
    if (!isInitialized) {
      console.log('Initializing store default path for batch downloads...');
      await initializeDefaultPath();
    }
    
    // Check for batch-specific saved path
    const batchPath = localStorage.getItem(LOCAL_STORAGE_BATCH_PATH_KEY);
    if (batchPath) {
      const isValid = await validateDownloadPath(batchPath);
      if (isValid) {
        setCurrentDownloadPath(batchPath);
        setIsLoadingPath(false);
        return;
      }
      toast.warn('Previously saved batch download path is invalid. Reverting to default.');
      localStorage.removeItem(LOCAL_STORAGE_BATCH_PATH_KEY);
    }
    
    // Check for general download path from single download form
    const generalPath = localStorage.getItem('flowdownload_defaultDownloadPath');
    if (generalPath) {
      const isValid = await validateDownloadPath(generalPath);
      if (isValid) {
        setCurrentDownloadPath(generalPath);
        localStorage.setItem(LOCAL_STORAGE_BATCH_PATH_KEY, generalPath);
        setIsLoadingPath(false);
        return;
      }
    }
    
    // Use the store's initialized default path
    if (defaultDownloadPath && defaultDownloadPath !== 'Downloads') {
      const isValid = await validateDownloadPath(defaultDownloadPath);
      if (isValid) {
        setCurrentDownloadPath(defaultDownloadPath);
        localStorage.setItem(LOCAL_STORAGE_BATCH_PATH_KEY, defaultDownloadPath);
        setIsLoadingPath(false);
        return;
      }
    }
    
    // If still no valid path, set to store's default
    setCurrentDownloadPath(defaultDownloadPath);
    setIsLoadingPath(false);
  }, [defaultDownloadPath, setDefaultDownloadPath, initializeDefaultPath, isInitialized]);

  useEffect(() => {
    initializePath();
  }, [initializePath]);

  const handleSelectFolder = async () => {
    setIsSelectingFolder(true);
    try {
      const selectedPath = await selectDownloadFolder();
      if (selectedPath) {
        const isValid = await validateDownloadPath(selectedPath);
        if (isValid) {
          setCurrentDownloadPath(selectedPath);
          localStorage.setItem(LOCAL_STORAGE_BATCH_PATH_KEY, selectedPath);
          toast.success('Batch download folder updated and saved.');
        } else {
          toast.error('The selected folder is not valid. Please choose a different one.');
        }
      }
    } catch (error) {
      console.error('Error during folder selection process:', error);
      toast.error('An unexpected error occurred while selecting the folder.');
    } finally {
      setIsSelectingFolder(false);
    }
  };

  const handleAddUrl = () => {
    setUrls([...urls, '']);
  };

  const handleRemoveUrl = (index: number) => {
    setUrls(urls.filter((_, i) => i !== index));
  };

  const handleUrlChange = (index: number, value: string) => {
    const newUrls = [...urls];
    newUrls[index] = value;
    setUrls(newUrls);
  };

  const extractFilenameFromUrl = (url: string): string => {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const filename = pathname.split('/').pop() || 'download';
      // Remove extension and clean up
      return filename.replace(/\.[^/.]+$/, '').replace(/[^a-zA-Z0-9_-]/g, '_');
    } catch {
      return `download_${Date.now()}`;
    }
  };

  const parseUrlsFromText = (text: string): string[] => {
    // Split by newlines and filter out empty lines
    const lines = text.split('\n').filter(line => line.trim());
    
    // Extract URLs from each line (handles mixed text with URLs)
    const extractedUrls: string[] = [];
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    
    lines.forEach(line => {
      const matches = line.match(urlRegex);
      if (matches) {
        extractedUrls.push(...matches);
      }
    });
    
    return extractedUrls;
  };

  const handleBatchDownload = async () => {
    let urlsToDownload: string[] = [];

    if (isBulkMode) {
      // Parse URLs from bulk text
      urlsToDownload = parseUrlsFromText(bulkText);
      if (urlsToDownload.length === 0) {
        toast.error('No valid URLs found in the text');
        return;
      }
    } else {
      // Use individual URL inputs
      urlsToDownload = urls.filter(url => url.trim());
      if (urlsToDownload.length === 0) {
        toast.error('Please enter at least one URL');
        return;
      }
    }

    if (!currentDownloadPath.trim()) {
      toast.error('Please select a valid download location.');
      return;
    }

    // Start all downloads
    let successCount = 0;
    for (const url of urlsToDownload) {
      try {
        const filename = extractFilenameFromUrl(url) + '.mp4';
        // Make sure the path is resolved properly
        const resolvedPath = await resolvePath(currentDownloadPath);
        console.log('Starting batch download with resolved path:', resolvedPath);
        
        // Validate that we can write to this path
        const isValid = await validateDownloadPath(resolvedPath);
        if (!isValid) {
          toast.error('Cannot write to the selected download location. Please choose another.');
          return;
        }
        
        await addDownload(url.trim(), filename, quality, resolvedPath);
        successCount++;
      } catch (error) {
        console.error(`Failed to add download for ${url}:`, error);
        toast.error(`Failed to add: ${url}`);
      }
    }

    if (successCount > 0) {
      toast.success(`Started ${successCount} download${successCount > 1 ? 's' : ''}`);
      
      // Clear the form
      if (isBulkMode) {
        setBulkText('');
      } else {
        setUrls(['']);
      }
    }
  };

  const handlePasteFromClipboard = async () => {
    try {
      const text = await navigator.clipboard.readText();
      setBulkText(text);
      const extractedUrls = parseUrlsFromText(text);
      if (extractedUrls.length > 0) {
        toast.info(`Found ${extractedUrls.length} URL${extractedUrls.length > 1 ? 's' : ''}`);
      }
    } catch (error) {
      toast.error('Failed to read clipboard');
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
          <FiList className="mr-2" />
          Batch Download
        </h2>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsBulkMode(!isBulkMode)}
            className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
              isBulkMode 
                ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300' 
                : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
            }`}
          >
            {isBulkMode ? 'Bulk Mode' : 'Individual Mode'}
          </button>
          
          <select
            value={quality}
            onChange={(e) => setQuality(e.target.value)}
            className="px-3 py-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            {qualityOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Download Location Selector */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Download Location
        </label>
        <div className="flex gap-2">
          <div className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-sm text-gray-700 dark:text-gray-300 flex items-center min-h-[38px]">
            <FiFolder className="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" />
            <span className="truncate" title={currentDownloadPath}>
              {isLoadingPath ? 'Loading path...' : (currentDownloadPath ? formatPathForDisplay(currentDownloadPath, 60) : 'Please select a folder...')}
            </span>
          </div>
          <button
            type="button"
            onClick={handleSelectFolder}
            disabled={isSelectingFolder || isLoadingPath}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0"
          >
            {isSelectingFolder ? (
              <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            ) : (
              'Browse'
            )}
          </button>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Batch downloads will be saved to this location. This setting is saved separately from single downloads.
        </p>
      </div>

      {isBulkMode ? (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Paste URLs (one per line or mixed with text)
            </label>
            <textarea
              value={bulkText}
              onChange={(e) => setBulkText(e.target.value)}
              placeholder="Paste multiple URLs here...&#10;https://youtube.com/watch?v=...&#10;https://vimeo.com/...&#10;Or paste a playlist URL"
              className="w-full h-32 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={handlePasteFromClipboard}
              className="mt-2 flex items-center px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
            >
              <FiCopy className="mr-1" />
              Paste from clipboard
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-3">
          {urls.map((url, index) => (
            <div key={index} className="flex items-center space-x-2">
              <input
                type="text"
                value={url}
                onChange={(e) => handleUrlChange(index, e.target.value)}
                placeholder="Enter URL..."
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {urls.length > 1 && (
                <button
                  onClick={() => handleRemoveUrl(index)}
                  className="p-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                >
                  <FiTrash2 />
                </button>
              )}
            </div>
          ))}
          
          <button
            onClick={handleAddUrl}
            className="flex items-center px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
          >
            <FiPlus className="mr-1" />
            Add another URL
          </button>
        </div>
      )}

      <div className="mt-6">
        <button
          onClick={handleBatchDownload}
          disabled={isLoadingPath || !currentDownloadPath}
          className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-blue-600"
        >
          <FiDownload className="mr-2" />
          Start Batch Download
        </button>
      </div>

      {/* Info section */}
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
        <p className="text-xs text-blue-700 dark:text-blue-300">
          <strong>Tip:</strong> For playlists, just paste the playlist URL and all videos will be downloaded. 
          Downloads run in parallel for maximum speed (up to 32 concurrent fragments per video).
        </p>
      </div>
    </div>
  );
};

export default BatchDownload;