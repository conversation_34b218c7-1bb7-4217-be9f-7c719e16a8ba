import React, { useState, useRef, useEffect } from 'react';
import { toast } from 'react-toastify';
import { Download, useDownloadStore } from '../store/downloadStore';
import { formatPathForDisplay } from '../utils/folderUtils';
import { withTauriAPIs, ensureFolderAccessible } from '../utils/tauriUtils';

interface DownloadItemProps {
  download: Download;
}


// Helper function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// Helper function to get file type and icon
const getFileTypeInfo = (filename: string) => {
  const extension = filename.split('.').pop()?.toLowerCase() || '';
  
  const fileTypes: Record<string, { icon: string; color: string; label: string }> = {
    // Video files
    'mp4': { icon: '🎬', color: 'text-red-600', label: 'Video' },
    'mkv': { icon: '🎬', color: 'text-red-600', label: 'Video' },
    'webm': { icon: '🎬', color: 'text-red-600', label: 'Video' },
    'avi': { icon: '🎬', color: 'text-red-600', label: 'Video' },
    'mov': { icon: '🎬', color: 'text-red-600', label: 'Video' },
    'wmv': { icon: '🎬', color: 'text-red-600', label: 'Video' },
    
    // Audio files
    'mp3': { icon: '🎵', color: 'text-purple-600', label: 'Audio' },
    'wav': { icon: '🎵', color: 'text-purple-600', label: 'Audio' },
    'flac': { icon: '🎵', color: 'text-purple-600', label: 'Audio' },
    'm4a': { icon: '🎵', color: 'text-purple-600', label: 'Audio' },
    
    // Documents
    'pdf': { icon: '📄', color: 'text-red-500', label: 'PDF' },
    'doc': { icon: '📝', color: 'text-blue-600', label: 'Document' },
    'docx': { icon: '📝', color: 'text-blue-600', label: 'Document' },
    'txt': { icon: '📝', color: 'text-gray-600', label: 'Text' },
    'rtf': { icon: '📝', color: 'text-gray-600', label: 'Document' },
    
    // Presentations
    'ppt': { icon: '📊', color: 'text-orange-600', label: 'Presentation' },
    'pptx': { icon: '📊', color: 'text-orange-600', label: 'Presentation' },
    
    // Spreadsheets
    'xls': { icon: '📈', color: 'text-green-600', label: 'Spreadsheet' },
    'xlsx': { icon: '📈', color: 'text-green-600', label: 'Spreadsheet' },
    'csv': { icon: '📈', color: 'text-green-600', label: 'Data' },
    
    // Images
    'jpg': { icon: '🖼️', color: 'text-pink-600', label: 'Image' },
    'jpeg': { icon: '🖼️', color: 'text-pink-600', label: 'Image' },
    'png': { icon: '🖼️', color: 'text-pink-600', label: 'Image' },
    'gif': { icon: '🖼️', color: 'text-pink-600', label: 'Image' },
    'webp': { icon: '🖼️', color: 'text-pink-600', label: 'Image' },
    
    // Archives
    'zip': { icon: '📦', color: 'text-yellow-600', label: 'Archive' },
    'rar': { icon: '📦', color: 'text-yellow-600', label: 'Archive' },
    '7z': { icon: '📦', color: 'text-yellow-600', label: 'Archive' },
    'tar': { icon: '📦', color: 'text-yellow-600', label: 'Archive' },
    
    // Software
    'exe': { icon: '⚙️', color: 'text-gray-700', label: 'Software' },
    'msi': { icon: '⚙️', color: 'text-gray-700', label: 'Installer' },
    'dmg': { icon: '💿', color: 'text-gray-700', label: 'Disk Image' },
    'pkg': { icon: '📦', color: 'text-gray-700', label: 'Package' },
    
    // E-books
    'epub': { icon: '📚', color: 'text-indigo-600', label: 'E-book' },
    'mobi': { icon: '📚', color: 'text-indigo-600', label: 'E-book' },
    
    // Code
    'js': { icon: '💻', color: 'text-yellow-500', label: 'JavaScript' },
    'ts': { icon: '💻', color: 'text-blue-500', label: 'TypeScript' },
    'py': { icon: '💻', color: 'text-green-500', label: 'Python' },
    'html': { icon: '💻', color: 'text-orange-500', label: 'HTML' },
    'css': { icon: '💻', color: 'text-blue-400', label: 'CSS' },
  };
  
  return fileTypes[extension] || { icon: '📄', color: 'text-gray-500', label: 'File' };
};

// Helper function to determine if an error is related to missing tools
const isMissingToolsError = (error?: string): boolean => {
  if (!error) return false;
  return error.includes('yt-dlp') || 
         error.includes('gallery-dl') || 
         error.includes('instaloader') ||
         error.includes('require') && error.includes('install');
};

// Helper function to get tool installation instructions
const getToolInstallInstructions = (error?: string): string => {
  if (!error) return '';
  
  let instructions = 'To install the required tools:\n\n';
  
  if (error.includes('yt-dlp')) {
    instructions += '**For yt-dlp (YouTube, Twitter, Facebook):**\n';
    instructions += '- **macOS**: `brew install yt-dlp`\n';
    instructions += '- **Windows**: `pip install yt-dlp`\n';
    instructions += '- **Linux**: `sudo apt install yt-dlp` or `pip install yt-dlp`\n\n';
  }
  
  if (error.includes('gallery-dl')) {
    instructions += '**For gallery-dl (Instagram, Twitter):**\n';
    instructions += '- **macOS**: `brew install gallery-dl`\n';
    instructions += '- **Windows**: `pip install gallery-dl`\n';
    instructions += '- **Linux**: `pip install gallery-dl`\n\n';
  }
  
  if (error.includes('instaloader')) {
    instructions += '**For instaloader (Instagram):**\n';
    instructions += '- **macOS**: `brew install instaloader`\n';
    instructions += '- **Windows**: `pip install instaloader`\n';
    instructions += '- **Linux**: `pip install instaloader`\n\n';
  }
  
  instructions += 'After installing, restart FlowDownload and try again.';
  
  return instructions;
};

const DownloadItem: React.FC<DownloadItemProps> = ({ download }) => {
  const { removeDownload } = useDownloadStore();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // Debug logging for progress updates
  React.useEffect(() => {
    console.log(`🎨 DownloadItem render - ID: ${download.id}, Status: ${download.status}, Progress: ${download.progress}%`);
  }, [download.progress, download.id, download.status]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  /**
   * Check if we're running in a Tauri environment by attempting to import Tauri APIs
   */
  const isInTauriEnvironment = async (): Promise<boolean> => {
    try {
      await import('@tauri-apps/api/core');
      return true;
    } catch {
      return false;
    }
  };

  const getStatusColor = (status: Download['status']) => {
    switch (status) {
      case 'downloading':
        return 'text-blue-600';
      case 'completed':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      case 'paused':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: Download['status']) => {
    switch (status) {
      case 'downloading':
        return (
          <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        );
      case 'completed':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case 'pending':
        return (
          <svg className="w-4 h-4 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  // Helper function for status messages - currently unused but kept for future use
  // const getStatusMessage = (currentDownload: Download) => {
  //   switch (currentDownload.status) {
  //     case 'pending':
  //       return 'Analyzing URL and preparing download...';
  //     case 'downloading':
  //       return `Downloading at ${currentDownload.downloadSpeed || 'calculating speed...'}`;
  //     case 'completed':
  //       return `Download completed in ${currentDownload.completedAt ? getTimeDifference(currentDownload.createdAt, currentDownload.completedAt) : 'unknown time'}`;
  //     case 'error':
  //       return 'Download failed - check URL or try again';
  //     case 'paused':
  //       return 'Download paused - click to resume';
  //     default:
  //       return 'Waiting to start...';
  //   }
  // };

  const getTimeDifference = (start: Date, end: Date) => {
    const diff = Math.abs(new Date(end).getTime() - new Date(start).getTime());
    const seconds = Math.floor(diff / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    return `${minutes}m ${seconds % 60}s`;
  };

  const getQualityBadgeColor = (quality: string) => {
    if (quality === 'auto') return 'bg-blue-100 text-blue-800';
    if (['2160p', '1440p'].includes(quality)) return 'bg-purple-100 text-purple-800';
    if (['1080p', '720p'].includes(quality)) return 'bg-green-100 text-green-800';
    return 'bg-gray-100 text-gray-800';
  };

  const handleOpenFile = async () => {
    setIsDropdownOpen(false);

    if (download.status !== 'completed') {
      toast.warn('File can only be opened after download completes.');
      return;
    }

    if (!download.downloadPath || !download.filename) {
        toast.error('Download path or filename is missing.');
        return;
    }

    if (await isInTauriEnvironment()) {
      try {
        const { open } = await import('@tauri-apps/plugin-shell');
        const { exists } = await import('@tauri-apps/plugin-fs');
        const filePath = `${download.downloadPath}/${download.filename}`;

        if (await exists(filePath)) {
          await open(filePath);
          toast.success(`Opening file: ${download.filename}`);
        } else {
          toast.error(`File not found: ${filePath}. It may have been moved or deleted.`);
        }
      } catch (error) {
        console.error('Error opening file in Tauri:', error);
        toast.error('Could not open file. Ensure you have permission and the file exists.');
      }
    } else {
      toast.info(`Simulating: Would open file '${download.filename}' from '${download.downloadPath}'`);
    }
  };

  const handleOpenFolder = async () => {
    setIsDropdownOpen(false);

    if (!download.downloadPath) {
      toast.error('Download path is missing.');
      return;
    }

    try {
      await withTauriAPIs(
        async ({ invoke, open }) => {
          // Ensure the folder exists and is accessible
          const isAccessible = await ensureFolderAccessible(download.downloadPath, invoke);
          if (!isAccessible) {
            toast.error(`Folder is not accessible and could not be created: ${formatPathForDisplay(download.downloadPath)}`);
            return;
          }
          
          // Use the enhanced folder opening that highlights the file
          if (download.status === 'completed' && download.filename) {
            try {
              await invoke('open_folder_and_highlight_file', { 
                folder_path: download.downloadPath,
                filename: download.filename 
              });
              toast.success(`Opening folder and highlighting: ${download.filename}`);
            } catch (explorerError) {
              console.warn('Failed to open with file highlighting, falling back to simple folder open:', explorerError);
              try {
                await invoke('open_folder_in_explorer', { path: download.downloadPath });
                toast.success(`Opening folder: ${formatPathForDisplay(download.downloadPath)}`);
              } catch (fallbackError) {
                await open(download.downloadPath);
                toast.success(`Opening folder: ${formatPathForDisplay(download.downloadPath)}`);
              }
            }
          } else {
            await open(download.downloadPath);
            toast.success(`Opening folder: ${formatPathForDisplay(download.downloadPath)}`);
          }
        },
        () => {
          toast.info(`Simulating: Would open folder '${download.downloadPath}' and highlight '${download.filename}'`);
        }
      );
    } catch (error) {
      console.error('Error opening folder:', error);
      toast.error(`Could not open folder: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleDelete = () => {
    setIsDropdownOpen(false);
    removeDownload(download.id);
    toast.success(`Removed download for ${download.filename}`);
  };

  // Render error details for social media downloads
  const renderErrorDetails = () => {
    if (download.status !== 'error' || !download.error) return null;
    
    if (isMissingToolsError(download.error)) {
      // Sanitize error message to prevent XSS
      const safeError = download.error.replace(/[<>&"']/g, '');
      const safeInstructions = getToolInstallInstructions(download.error).replace(/[<>&"']/g, '');

      return (
        <div className="mt-2 text-sm border border-red-200 rounded-md p-2 bg-red-50">
          <div className="font-medium text-red-700 mb-1">{safeError}</div>
          <div className="text-xs text-gray-700 whitespace-pre-line">
            {safeInstructions}
          </div>
        </div>
      );
    }
    
    // Sanitize error message to prevent XSS
    const safeError = download.error.replace(/[<>&"']/g, '');
    return (
      <div className="mt-2 text-sm text-red-600">{safeError}</div>
    );
  };

  return (
    <div className="p-4 hover:bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-3">
            <div className={`flex-shrink-0 ${getStatusColor(download.status)}`}>
              {getStatusIcon(download.status)}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <p className="text-sm font-medium text-gray-900 truncate flex items-center" title={download.filename}>
                  <span className="mr-2 text-lg flex-shrink-0">{getFileTypeInfo(download.filename).icon}</span>
                  <span className="truncate">{download.filename}</span>
                </p>
                <div className="flex items-center space-x-1">
                  {/* Show progress badge prominently when downloading */}
                  {download.progress > 0 && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 animate-pulse">
                      {download.progress}%
                    </span>
                  )}
                  <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getQualityBadgeColor(download.quality)}`}>
                    {download.quality}
                  </span>
                  <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 ${getFileTypeInfo(download.filename).color}`}>
                    {getFileTypeInfo(download.filename).label}
                  </span>
                </div>
              </div>
              <p className="text-sm text-gray-500 truncate" title={download.url}>
                {download.url}
              </p>
              
              {/* Download path */}
              <div className="flex items-center mt-1 text-xs text-gray-500">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 002-2h14a2 2 0 002 2v2" />
                </svg>
                <span className="truncate" title={download.downloadPath}>
                  {download.downloadPath ? formatPathForDisplay(download.downloadPath, 50) : 'Path not set'}
                </span>
              </div>
              
              {/* Download details - Enhanced with more metrics */}
              <div className="flex items-center flex-wrap gap-x-4 gap-y-1 mt-1 text-xs text-gray-500">
                {download.fileSize && (
                  <span className="flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    <span title="File Size">{download.fileSize}</span>
                  </span>
                )}
                {download.downloadSpeed && download.status === 'downloading' && (
                  <span className="flex items-center text-blue-600 font-medium">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <span title="Download Speed">{download.downloadSpeed}</span>
                  </span>
                )}
                {download.status === 'completed' && download.completedAt && (
                  <span className="flex items-center text-green-600">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span title="Download Duration">
                      {getTimeDifference(download.createdAt, download.completedAt)}
                    </span>
                  </span>
                )}
                {download.status === 'downloading' && (
                  <span className="flex items-center text-gray-600">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                    </svg>
                    <span title="Download Progress">{download.progress}%</span>
                  </span>
                )}
              </div>
            </div>
          </div>
          
          {/* Always show progress bar for downloading/pending status OR if there's progress */}
          {(download.progress >= 0 || download.status === 'downloading' || download.status === 'pending') && (
            <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="flex items-center justify-between text-xs mb-2">
                <span className="text-gray-700 dark:text-gray-300 font-semibold">
                  Progress: {download.progress || 0}%
                </span>
                <div className="flex items-center space-x-2">
                  {download.downloadSpeed && (
                    <span className="text-blue-600 dark:text-blue-400 font-medium">{download.downloadSpeed}</span>
                  )}
                  {download.fileSize && (
                    <span className="text-gray-600 dark:text-gray-400">{download.fileSize}</span>
                  )}
                </div>
              </div>
              
              {/* Progress bar container with border for visibility */}
              <div className="w-full bg-white dark:bg-gray-900 rounded-full h-4 border border-gray-300 dark:border-gray-600 overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-300 flex items-center justify-center"
                  style={{ 
                    width: `${Math.max(download.progress || 0, 1)}%`,
                    minWidth: download.progress > 0 ? '1%' : '0%'
                  }}
                >
                  {/* Always show percentage text */}
                  {download.progress > 5 && (
                    <span className="text-xs text-white font-bold px-1">
                      {download.progress}%
                    </span>
                  )}
                </div>
              </div>
              
              {/* Additional status text below progress bar */}
              <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
                Status: {download.status} | Progress: {download.progress || 0}%
                {download.estimatedTimeRemaining && (
                  <span className="ml-2">• ETA: {download.estimatedTimeRemaining}</span>
                )}
              </div>
              
              {/* Enhanced metrics display */}
              <div className="flex justify-between items-center mt-1.5">
                <div className="text-xs text-gray-500">
                  {download.bytesDownloaded !== undefined && download.bytesTotal !== undefined && (
                    <span className="font-medium">
                      {formatFileSize(download.bytesDownloaded)} of {formatFileSize(download.bytesTotal)}
                    </span>
                  )}
                </div>
                {download.estimatedTimeRemaining && (
                  <div className="text-xs text-gray-500">
                    <span className="flex items-center">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {download.estimatedTimeRemaining} remaining
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {download.status === 'error' && (
            <div className="mt-3">
              <div className="flex items-center justify-between text-xs mb-1">
                <span className="text-gray-600 font-medium">Download Failed</span>
                <span className="text-red-600 font-medium">Unknown size • Unknown time</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-red-500 h-2.5 rounded-full"
                  style={{ width: '100%' }}
                />
              </div>
            </div>
          )}

          {download.status === 'pending' && (
            <div className="mt-3">
              <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                <span>Processing</span>
                <span className="text-orange-600">Preparing...</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-orange-400 h-2.5 rounded-full animate-pulse" style={{ width: '30%' }} />
              </div>
              <div className="mt-2 text-xs text-gray-600">
                <div className="flex items-center space-x-1">
                  <svg className="w-3 h-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span>Analyzing video format and quality options...</span>
                </div>
              </div>
            </div>
          )}

          {download.status === 'completed' && (
            <div className="mt-2 text-xs text-green-600 bg-green-50 rounded-md p-2">
              <div className="flex flex-col space-y-1">
                <div className="flex items-center">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>Download completed!</span>
                </div>
                <div className="pl-4 flex flex-col">
                  <span className="flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 002-2h14a2 2 0 002 2v2" />
                    </svg>
                    <span>File saved to: {formatPathForDisplay(download.downloadPath, 60)}</span>
                  </span>
                  {download.fileSize && (
                    <span className="flex items-center">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                      <span>Size: {download.fileSize}</span>
                    </span>
                  )}
                  {download.completedAt && (
                    <span className="flex items-center">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>Time: {getTimeDifference(download.createdAt, download.completedAt)}</span>
                    </span>
                  )}
                </div>
              </div>
            </div>
          )}
          
          {renderErrorDetails()}
        </div>

        <div className="relative ml-4">
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="rounded-full p-1 text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none"
          >
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>

          {isDropdownOpen && (
            <div ref={dropdownRef} className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 z-10">
              {download.status === 'completed' && (
                <button
                  onClick={handleOpenFile}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                >
                  <svg className="h-4 w-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                  </svg>
                  Open File
                </button>
              )}
              <button
                onClick={handleOpenFolder}
                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
              >
                <svg className="h-4 w-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z" />
                </svg>
                Open Folder
              </button>
              <button
                onClick={handleDelete}
                className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
              >
                <svg className="h-4 w-4 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Remove
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DownloadItem;
