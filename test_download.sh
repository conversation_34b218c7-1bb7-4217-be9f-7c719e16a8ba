#!/bin/bash
# Test script to trigger a download and see debug output

echo "Testing download functionality..."

# Check if yt-dlp is working directly
echo "Direct yt-dlp test:"
yt-dlp --version

echo ""
echo "Testing a simple YouTube URL with yt-dlp directly:"
yt-dlp --simulate --quiet "https://www.youtube.com/watch?v=dQw4w9WgXcQ" || echo "Direct yt-dlp test failed"

echo ""
echo "Now testing through the app by watching logs..."

# This will test if the app is responsive
curl -X POST "http://localhost:1420" 2>/dev/null && echo "App is responding" || echo "App not responding on localhost:1420"